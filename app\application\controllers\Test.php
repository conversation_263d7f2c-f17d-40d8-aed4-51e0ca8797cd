<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Test extends CI_Controller {

  public function index() {
    $this->load->view('test/index');
  }

  // Comprobar configuraciones de MySQL
  public function verificar_max_allowed_packet() {
    $query_server = $this->db->query("SHOW VARIABLES LIKE 'max_allowed_packet';");
    $resultado_server = $query_server->row();

    $query_client = $this->db->query("SELECT @@session.max_allowed_packet AS client_max_allowed_packet;");
    $resultado_client = $query_client->row();

    $query_tmpdir = $this->db->query("SHOW VARIABLES LIKE 'tmpdir';");
    $resultado_tmpdir = $query_tmpdir->row();

    // Mostrar resultados
    echo "Servidor: " . $resultado_server->Value . "<br>";
    echo "Cliente: " . $resultado_client->client_max_allowed_packet . "<br>";
    echo "Tmpdir: " . $resultado_tmpdir->Value . "<br>";
  }

  // verificar la informacion de php
  public function verificar_info_php() {
    phpinfo();
  }

  // Mostrar estadísticas del sistema de caché utilizando Redis
  public function mostrar_estadisticas_cache() {
    // Usar Redis en lugar de Memcached
    $redis = new Redis();
    try {
      $redis->connect('127.0.0.1', 6379,);
      // agregar autentificacion
      $redis->auth('tHjGQeXrgmI45Y');
    } catch (RedisException $e) {
      echo "No se pudo conectar al servidor Redis. Error: " . $e->getMessage();
      return;
    }

    // Obtener estadísticas de Redis
    $stats = $redis->info();

    if ($stats) {
      // Mostrar estadísticas clave
      $uptime = isset($stats['uptime_in_seconds']) ? $stats['uptime_in_seconds'] : 0;
      $used_memory = isset($stats['used_memory']) ? $stats['used_memory'] : 0;
      $total_memory = isset($stats['maxmemory']) ? $stats['maxmemory'] : 0; // Si maxmemory no está configurado, Redis usará toda la memoria disponible

      // Hits y misses
      $hits = isset($stats['keyspace_hits']) ? $stats['keyspace_hits'] : 0;
      $misses = isset($stats['keyspace_misses']) ? $stats['keyspace_misses'] : 0;

      // Memoria usada como porcentaje
      $memory_percentage = $total_memory > 0 ? ($used_memory / $total_memory) * 100 : 0;

      // Mostrar las estadísticas de Redis
      echo "<h2>Estadísticas del sistema de caché (Redis)</h2>";
      echo "<p><strong>Consultas rápidas (hits):</strong> $hits</p>";
      echo "<p><strong>Consultas lentas (misses):</strong> $misses</p>";
      echo "<p><strong>Tiempo en funcionamiento:</strong> " . gmdate("H:i:s", $uptime) . " horas</p>";
      echo "<p><strong>Memoria utilizada:</strong> " . round($used_memory / (1024 * 1024), 2) . " MB de " . ($total_memory > 0 ? round($total_memory / (1024 * 1024), 2) . " MB" : "ilimitada") . "</p>";
      echo "<p><strong>Porcentaje de memoria utilizada:</strong> " . round($memory_percentage, 2) . "%</p>";

      // Representación visual de la memoria utilizada
      echo "<div style='width:300px; background-color:#ccc;'>
              <div style='width:" . round($memory_percentage, 2) . "%; background-color:green; height:20px;'></div>
            </div>";

    } else {
      echo "No se pudieron obtener las estadísticas del servidor Redis.";
    }
  }

  // Prueba básica de caché usando Redis
  public function prueba_cache() {
    // Cambiar el driver de cache a Redis
      // Cargar el driver de cache con la configuración
      $this->load->driver('cache', [
          'adapter' => $this->config->item('cache_driver'),  // 'redis'
          'backup'  => $this->config->item('cache_backup'),   // 'file'
          'redis'   => $this->config->item('redis')          // Array con host, password, port, timeout
      ]);

    $key = 'test_key';
    $data = 'Hola Caché en Redis';

    // Guardar en caché
    if ($this->cache->save($key, $data, 300)) { // 300 segundos = 5 minutos
      echo "Datos guardados en caché (Redis).<br>";
    } else {
      echo "Fallo al guardar en caché (Redis).<br>";
    }

    // Obtener de caché
    $cached_data = $this->cache->get($key);
    if ($cached_data) {
      echo "Datos en caché: " . $cached_data;
    } else {
      echo "No se encontraron datos en caché.";
    }
  }

  public function flush_all_cache(): void {
    // Cargar el driver de cache con la configuración
    $this->load->driver('cache', [
        'adapter' => $this->config->item('cache_driver'),  // 'redis'
        'backup'  => $this->config->item('cache_backup'),   // 'file'
        'redis'   => $this->config->item('redis')          // Array con host, password, port, timeout
    ]);

    // Limpiar toda la caché usando el método clean() del driver
    if ($this->cache->clean()) {
        echo "Redis cache flushed successfully.\n";
    } else {
        echo "Failed to flush Redis cache.\n";
    }
  }

  /**
   * Prueba el flujo completo de subida y acceso a archivos en R2
   *
   * Este método realiza las siguientes operaciones:
   * 1. Crea un archivo temporal de prueba
   * 2. Sube el archivo a R2 usando StorageFactory
   * 3. Obtiene la URL pública del archivo
   * 4. Verifica si el archivo existe en R2
   * 5. Muestra enlaces para descargar y eliminar el archivo
   *
   * @return void
   */
  public function test_r2_storage(): void {
    // Cargar las librerías necesarias
    $this->load->library('StorageFactory');

    // Obtener la instancia de almacenamiento
    try {
      $storage = $this->storagefactory->get_instance();
      echo "<h2>Prueba de R2 Storage</h2>";
      echo "<p>Tipo de almacenamiento: " . get_class($storage) . "</p>";
    } catch (Exception $e) {
      echo "<div style='color: red; font-weight: bold;'>Error al obtener instancia de almacenamiento: " . $e->getMessage() . "</div>";
      return;
    }

    // Crear un archivo temporal de prueba
    $temp_dir = get_setting("temp_file_path");
    $temp_file_name = "test_file_" . date("YmdHis") . ".txt";
    $temp_file_path = $temp_dir . $temp_file_name;

    // Escribir contenido en el archivo temporal
    $content = "Este es un archivo de prueba para R2 Storage.\n";
    $content .= "Fecha y hora: " . date("Y-m-d H:i:s") . "\n";
    $content .= "ID de prueba: " . uniqid() . "\n";

    if (file_put_contents($temp_file_path, $content) === false) {
      echo "<div style='color: red; font-weight: bold;'>Error al crear archivo temporal</div>";
      return;
    }

    echo "<p>Archivo temporal creado: $temp_file_path</p>";
    echo "<p>Contenido del archivo:</p>";
    echo "<pre>" . htmlspecialchars($content) . "</pre>";

    // Definir directorio de destino y nombre de archivo
    $destination_dir = "files/r2_test";
    $original_file_name = "test_file_" . date("YmdHis") . ".txt";

    // Subir el archivo a R2
    echo "<h3>Subiendo archivo a R2...</h3>";
    $result = $storage->save($temp_file_path, $destination_dir, $original_file_name);

    if ($result === false) {
      echo "<div style='color: red; font-weight: bold;'>Error al subir archivo a R2</div>";
      // Eliminar archivo temporal
      @unlink($temp_file_path);
      return;
    }

    echo "<p style='color: green;'>Archivo subido correctamente</p>";
    echo "<p>Resultado de save(): " . htmlspecialchars($result) . "</p>";

    // Construir la ruta completa del archivo en R2
    $file_key = $destination_dir . "/" . $result;
    echo "<p>File Key en R2: " . htmlspecialchars($file_key) . "</p>";

    // Obtener la URL pública del archivo
    try {
      $file_url = $storage->getFileUrl($file_key);
      echo "<p>URL pública del archivo: <a href='" . htmlspecialchars($file_url) . "' target='_blank'>" . htmlspecialchars($file_url) . "</a></p>";
    } catch (Exception $e) {
      echo "<div style='color: red; font-weight: bold;'>Error al obtener URL pública: " . $e->getMessage() . "</div>";
    }

    // Verificar si el archivo existe en R2
    try {
      $exists = $storage->fileExists($file_key);
      echo "<p>¿El archivo existe en R2? " . ($exists ? "Sí" : "No") . "</p>";
    } catch (Exception $e) {
      echo "<div style='color: red; font-weight: bold;'>Error al verificar existencia del archivo: " . $e->getMessage() . "</div>";
    }

    // Mostrar enlaces para descargar y eliminar el archivo
    echo "<h3>Acciones disponibles:</h3>";
    echo "<ul>";
    echo "<li><a href='" . site_url("test/download_r2_file?file_key=" . urlencode($file_key)) . "' target='_blank'>Descargar archivo</a></li>";
    echo "<li><a href='" . site_url("test/delete_r2_file?file_key=" . urlencode($file_key)) . "' onclick='return confirm(\"¿Estás seguro de eliminar este archivo?\")'>Eliminar archivo</a></li>";
    echo "</ul>";

    // Eliminar archivo temporal
    @unlink($temp_file_path);
    echo "<p>Archivo temporal eliminado</p>";

    // Guardar información en sesión para las otras funciones
    $this->session->set_userdata('r2_test_file_key', $file_key);
  }

  /**
   * Descarga un archivo de R2
   *
   * @return void
   */
  public function download_r2_file(): void {
    // Obtener file_key de la URL o de la sesión
    $file_key = $this->input->get('file_key');
    if (empty($file_key)) {
      $file_key = $this->session->userdata('r2_test_file_key');
      if (empty($file_key)) {
        echo "No se especificó un archivo para descargar";
        return;
      }
    }

    // Cargar las librerías necesarias
    $this->load->library('StorageFactory');

    // Obtener la instancia de almacenamiento
    try {
      $storage = $this->storagefactory->get_instance();

      // Verificar si el archivo existe
      if (!$storage->fileExists($file_key)) {
        echo "El archivo no existe en R2";
        return;
      }

      // Descargar el archivo
      $storage->download($file_key);

    } catch (Exception $e) {
      echo "Error al descargar archivo: " . $e->getMessage();
    }
  }

  /**
   * Elimina un archivo de R2
   *
   * @return void
   */
  public function delete_r2_file(): void {
    // Obtener file_key de la URL o de la sesión
    $file_key = $this->input->get('file_key');
    if (empty($file_key)) {
      $file_key = $this->session->userdata('r2_test_file_key');
      if (empty($file_key)) {
        echo "No se especificó un archivo para eliminar";
        return;
      }
    }

    // Cargar las librerías necesarias
    $this->load->library('StorageFactory');

    // Obtener la instancia de almacenamiento
    try {
      $storage = $this->storagefactory->get_instance();

      // Verificar si el archivo existe
      if (!$storage->fileExists($file_key)) {
        echo "<div style='color: red; font-weight: bold;'>El archivo no existe en R2</div>";
        echo "<p><a href='" . site_url("test/test_r2_storage") . "'>Volver a la prueba</a></p>";
        return;
      }

      // Eliminar el archivo
      $result = $storage->delete($file_key);

      if ($result) {
        echo "<div style='color: green; font-weight: bold;'>Archivo eliminado correctamente</div>";
      } else {
        echo "<div style='color: red; font-weight: bold;'>Error al eliminar archivo</div>";
      }

      echo "<p><a href='" . site_url("test/test_r2_storage") . "'>Volver a la prueba</a></p>";

    } catch (Exception $e) {
      echo "<div style='color: red; font-weight: bold;'>Error: " . $e->getMessage() . "</div>";
      echo "<p><a href='" . site_url("test/test_r2_storage") . "'>Volver a la prueba</a></p>";
    }
  }

  /**
   * Estadísticas avanzadas del sistema de caché con aislamiento por usuario
   * Solo accesible para administradores
   *
   * @return void
   */
  public function estadisticas_cache_avanzadas(): void {
    // Validación de permisos - Solo administradores
    // if ($this->login_user->cargo !== "TI") {
    //   $this->output->set_status_header(403);
    //   $this->output->set_content_type('application/json');
    //   echo json_encode([
    //     'error' => 'Acceso denegado',
    //     'message' => 'Solo los administradores pueden acceder a las estadísticas avanzadas de caché',
    //     'timestamp' => date('Y-m-d H:i:s')
    //   ], JSON_PRETTY_PRINT);
    //   return;
    // }

    try {
      // Cargar librerías necesarias
      $this->load->library('Cache_abstraction_layer');
      $this->load->library('Cache_service');
      $this->load->library('Predis_cache_service');
      $this->load->model('Users_model');

      // Obtener estadísticas del sistema
      $cache_stats = $this->_obtener_estadisticas_sistema();
      $isolation_stats = $this->_obtener_estadisticas_aislamiento();
      $performance_stats = $this->_obtener_estadisticas_rendimiento();
      $memory_stats = $this->_obtener_estadisticas_memoria();
      $collision_detection = $this->_detectar_colisiones_cache();
      $ttl_analysis = $this->_analizar_ttl_cache();

      // Compilar respuesta completa
      $response = [
        'status' => 'success',
        'timestamp' => date('Y-m-d H:i:s'),
        'admin_user' => [
          'id' => $this->login_user->id,
          'email' => $this->login_user->email ?? 'N/A',
          'name' => ($this->login_user->first_name ?? '') . ' ' . ($this->login_user->last_name ?? '')
        ],
        'cache_system' => $cache_stats,
        'user_isolation' => $isolation_stats,
        'performance_metrics' => $performance_stats,
        'memory_usage' => $memory_stats,
        'collision_detection' => $collision_detection,
        'ttl_analysis' => $ttl_analysis,
        'recommendations' => $this->_generar_recomendaciones($cache_stats, $performance_stats, $memory_stats)
      ];

      // Configurar respuesta JSON
      $this->output->set_content_type('application/json');
      echo json_encode($response, JSON_PRETTY_PRINT);

    } catch (Exception $e) {
      $this->output->set_status_header(500);
      $this->output->set_content_type('application/json');
      echo json_encode([
        'error' => 'Error interno del servidor',
        'message' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
      ], JSON_PRETTY_PRINT);
    }
  }

  /**
   * Prueba específicamente la funcionalidad de getFileUrl() con diferentes tipos de entradas
   *
   * @return void
   */
  public function test_get_file_url(): void {
    // Cargar las librerías necesarias
    $this->load->library('StorageFactory');

    // Obtener la instancia de almacenamiento
    try {
      $storage = $this->storagefactory->get_instance();
      echo "<h2>Prueba de getFileUrl()</h2>";
      echo "<p>Tipo de almacenamiento: " . get_class($storage) . "</p>";
    } catch (Exception $e) {
      echo "<div style='color: red; font-weight: bold;'>Error al obtener instancia de almacenamiento: " . $e->getMessage() . "</div>";
      return;
    }

    // Obtener el file_key de la sesión si existe
    $file_key = $this->session->userdata('r2_test_file_key');

    // Casos de prueba
    $test_cases = [
      [
        'name' => 'Archivo de prueba de sesión',
        'key' => $file_key,
        'description' => 'Archivo subido previamente (guardado en sesión)'
      ],
      [
        'name' => 'Ruta completa con directorio',
        'key' => 'files/r2_test/test_file.txt',
        'description' => 'Ruta completa con directorio y nombre de archivo'
      ],
      [
        'name' => 'Solo nombre de archivo',
        'key' => 'test_file.txt',
        'description' => 'Solo nombre de archivo sin directorio'
      ],
      [
        'name' => 'Nombre de archivo con prefijo',
        'key' => 'file6500af40acf96-test.pdf',
        'description' => 'Nombre de archivo con prefijo generado por el sistema'
      ],
      [
        'name' => 'Ruta con múltiples niveles',
        'key' => 'files/mimasoft_files/client_1/project_1/form_3/elemento_11603/file6500af40acf96-test.pdf',
        'description' => 'Ruta completa con múltiples niveles de directorios'
      ]
    ];

    echo "<style>
      table { border-collapse: collapse; width: 100%; margin-top: 20px; }
      th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
      th { background-color: #f2f2f2; }
      tr:nth-child(even) { background-color: #f9f9f9; }
      .success { color: green; }
      .error { color: red; }
    </style>";

    echo "<table>";
    echo "<tr><th>Caso de prueba</th><th>Entrada</th><th>Descripción</th><th>Resultado</th></tr>";

    foreach ($test_cases as $case) {
      echo "<tr>";
      echo "<td>" . htmlspecialchars($case['name']) . "</td>";
      echo "<td><code>" . htmlspecialchars($case['key']) . "</code></td>";
      echo "<td>" . htmlspecialchars($case['description']) . "</td>";

      try {
        if (empty($case['key'])) {
          echo "<td class='error'>Clave vacía - No se puede probar</td>";
        } else {
          $url = $storage->getFileUrl($case['key']);
          echo "<td class='success'>";
          echo "URL: <a href='" . htmlspecialchars($url) . "' target='_blank'>" . htmlspecialchars($url) . "</a>";
          echo "</td>";
        }
      } catch (Exception $e) {
        echo "<td class='error'>Error: " . htmlspecialchars($e->getMessage()) . "</td>";
      }

      echo "</tr>";
    }

    echo "</table>";

    // Formulario para probar con una entrada personalizada
    echo "<h3>Probar con una entrada personalizada</h3>";
    echo "<form method='post' action='" . site_url("test/test_get_file_url_custom") . "'>";
    echo "<div style='margin-bottom: 10px;'>";
    echo "<label for='custom_key'>Ruta o nombre de archivo:</label><br>";
    echo "<input type='text' id='custom_key' name='custom_key' style='width: 100%; padding: 8px; margin-top: 5px;' placeholder='Ejemplo: files/r2_test/mi_archivo.txt'>";
    echo "</div>";
    echo "<button type='submit' style='padding: 8px 15px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;'>Probar</button>";
    echo "</form>";

    echo "<p><a href='" . site_url("test") . "' style='display: inline-block; margin-top: 20px; padding: 8px 15px; background-color: #f0f8ff; border-radius: 4px; border: 1px solid #cce5ff; text-decoration: none;'>Volver a la página de pruebas</a></p>";
  }

  /**
   * Procesa la entrada personalizada para probar getFileUrl()
   *
   * @return void
   */
  public function test_get_file_url_custom(): void {
    $custom_key = $this->input->post('custom_key');

    if (empty($custom_key)) {
      echo "<div style='color: red; font-weight: bold;'>Error: No se proporcionó una ruta o nombre de archivo</div>";
      echo "<p><a href='" . site_url("test/test_get_file_url") . "'>Volver a la prueba</a></p>";
      return;
    }

    // Cargar las librerías necesarias
    $this->load->library('StorageFactory');

    // Obtener la instancia de almacenamiento
    try {
      $storage = $this->storagefactory->get_instance();
      echo "<h2>Resultado de la prueba personalizada</h2>";
      echo "<p>Entrada: <code>" . htmlspecialchars($custom_key) . "</code></p>";

      $url = $storage->getFileUrl($custom_key);
      echo "<p style='color: green;'>URL generada: <a href='" . htmlspecialchars($url) . "' target='_blank'>" . htmlspecialchars($url) . "</a></p>";

      // Intentar verificar si el archivo existe
      try {
        $exists = $storage->fileExists($custom_key);
        echo "<p>¿El archivo existe en R2? <strong>" . ($exists ? "Sí" : "No") . "</strong></p>";
      } catch (Exception $e) {
        echo "<p style='color: orange;'>No se pudo verificar la existencia del archivo: " . $e->getMessage() . "</p>";
      }

    } catch (Exception $e) {
      echo "<div style='color: red; font-weight: bold;'>Error: " . $e->getMessage() . "</div>";
    }

    echo "<p><a href='" . site_url("test/test_get_file_url") . "'>Volver a la prueba</a></p>";
  }

  /**
   * Obtiene estadísticas del sistema de caché
   *
   * @return array
   */
  private function _obtener_estadisticas_sistema(): array {
    try {
      // Estadísticas de Predis/Redis
      $predis_stats = $this->predis_cache_service->get_stats();

      // Información del driver activo
      $driver_info = $this->cache_abstraction_layer->get_driver_info();

      // Configuración actual
      $config = [
        'use_predis_cache' => $this->config->item('use_predis_cache'),
        'cache_driver' => $this->config->item('cache_driver'),
        'cache_backup' => $this->config->item('cache_backup'),
        'redis_config' => $this->config->item('redis')
      ];

      return [
        'driver_info' => $driver_info,
        'predis_stats' => $predis_stats,
        'configuration' => $config,
        'connection_status' => $predis_stats['connected'] ?? false,
        'redis_version' => $predis_stats['redis_version'] ?? 'unknown'
      ];
    } catch (Exception $e) {
      return [
        'error' => 'Error obteniendo estadísticas del sistema',
        'details' => $e->getMessage()
      ];
    }
  }

  /**
   * Obtiene estadísticas de aislamiento por usuario
   *
   * @return array
   */
  private function _obtener_estadisticas_aislamiento(): array {
    try {
      // Asegurar que las librerías y modelos estén cargados
      if (!isset($this->cache_service)) {
        $this->load->library('Cache_service');
      }
      if (!isset($this->users_model)) {
        $this->load->model('Users_model');
      }

      // Estadísticas del Cache_service
      $cache_service_status = $this->cache_service->get_isolation_status();

      // Estadísticas del Users_model
      $users_cache_status = $this->users_model->get_users_cache_status();

      // Información del contexto actual
      $current_context = $this->cache_service->get_cache_context_info();

      // Verificar configuración de aislamiento
      $isolation_config = $this->config->item('cache_isolation');

      return [
        'cache_service_status' => $cache_service_status,
        'users_model_status' => $users_cache_status,
        'current_context' => $current_context,
        'isolation_config' => $isolation_config,
        'isolation_enabled' => $cache_service_status['isolation_enabled'] ?? false,
        'context_detection' => [
          'user_id' => $current_context['user_context']['user_id'] ?? null,
          'client_id' => $current_context['user_context']['client_id'] ?? null,
          'user_type' => $current_context['user_context']['user_type'] ?? null,
          'is_admin' => $current_context['user_context']['is_admin'] ?? false
        ]
      ];
    } catch (Exception $e) {
      return [
        'error' => 'Error obteniendo estadísticas de aislamiento',
        'details' => $e->getMessage()
      ];
    }
  }

  /**
   * Obtiene métricas de rendimiento del caché
   *
   * @return array
   */
  private function _obtener_estadisticas_rendimiento(): array {
    try {
      $predis_stats = $this->predis_cache_service->get_stats();

      $hits = (int)($predis_stats['keyspace_hits'] ?? 0);
      $misses = (int)($predis_stats['keyspace_misses'] ?? 0);
      $total_requests = $hits + $misses;

      $hit_rate = $total_requests > 0 ? round(($hits / $total_requests) * 100, 2) : 0;
      $miss_rate = $total_requests > 0 ? round(($misses / $total_requests) * 100, 2) : 0;

      // Calcular tiempo promedio de respuesta (estimado)
      $commands_processed = (int)($predis_stats['total_commands_processed'] ?? 0);
      $uptime = (int)($predis_stats['uptime_in_seconds'] ?? 1);
      $avg_commands_per_second = $uptime > 0 ? round($commands_processed / $uptime, 2) : 0;

      return [
        'cache_hits' => $hits,
        'cache_misses' => $misses,
        'total_requests' => $total_requests,
        'hit_rate_percentage' => $hit_rate,
        'miss_rate_percentage' => $miss_rate,
        'total_commands_processed' => $commands_processed,
        'average_commands_per_second' => $avg_commands_per_second,
        'uptime_seconds' => $uptime,
        'uptime_formatted' => $this->_format_uptime($uptime),
        'performance_rating' => $this->_calcular_rating_rendimiento($hit_rate, $avg_commands_per_second)
      ];
    } catch (Exception $e) {
      return [
        'error' => 'Error obteniendo métricas de rendimiento',
        'details' => $e->getMessage()
      ];
    }
  }

  /**
   * Obtiene estadísticas de uso de memoria
   *
   * @return array
   */
  private function _obtener_estadisticas_memoria(): array {
    try {
      $predis_stats = $this->predis_cache_service->get_stats();

      // Obtener información detallada de Redis
      $predis_client = $this->predis_cache_service->get_predis_client();
      $redis_info = $predis_client ? $predis_client->info() : [];

      $used_memory_bytes = (int)($redis_info['used_memory'] ?? 0);
      $used_memory_peak_bytes = (int)($redis_info['used_memory_peak'] ?? 0);
      $max_memory_bytes = (int)($redis_info['maxmemory'] ?? 0);

      $memory_usage_percentage = $max_memory_bytes > 0 ?
        round(($used_memory_bytes / $max_memory_bytes) * 100, 2) : 0;

      return [
        'used_memory_human' => $predis_stats['used_memory'] ?? 'unknown',
        'used_memory_peak_human' => $predis_stats['used_memory_peak'] ?? 'unknown',
        'used_memory_bytes' => $used_memory_bytes,
        'used_memory_peak_bytes' => $used_memory_peak_bytes,
        'max_memory_bytes' => $max_memory_bytes,
        'memory_usage_percentage' => $memory_usage_percentage,
        'connected_clients' => (int)($predis_stats['connected_clients'] ?? 0),
        'memory_fragmentation_ratio' => (float)($redis_info['mem_fragmentation_ratio'] ?? 0),
        'memory_efficiency' => $this->_calcular_eficiencia_memoria($memory_usage_percentage),
        'memory_recommendations' => $this->_generar_recomendaciones_memoria($memory_usage_percentage, $used_memory_bytes)
      ];
    } catch (Exception $e) {
      return [
        'error' => 'Error obteniendo estadísticas de memoria',
        'details' => $e->getMessage()
      ];
    }
  }

  /**
   * Detecta posibles colisiones de caché entre usuarios
   *
   * @return array
   */
  private function _detectar_colisiones_cache(): array {
    try {
      $predis_client = $this->predis_cache_service->get_predis_client();
      if (!$predis_client) {
        return ['error' => 'Cliente Predis no disponible'];
      }

      // Obtener muestra de claves del caché
      $sample_keys = $predis_client->keys('*');
      $total_keys = count($sample_keys);

      // Analizar patrones de claves
      $key_patterns = [];
      $user_contexts = [];
      $potential_collisions = [];

      foreach (array_slice($sample_keys, 0, 100) as $key) { // Limitar a 100 claves para análisis
        // Analizar patrón de la clave
        if (preg_match('/^([^:]+):(.*)$/', $key, $matches)) {
          $prefix = $matches[1];
          $suffix = $matches[2];

          $key_patterns[$prefix] = ($key_patterns[$prefix] ?? 0) + 1;

          // Detectar contexto de usuario en la clave
          if (preg_match('/user_([a-f0-9]{8})/', $suffix, $user_matches)) {
            $user_hash = $user_matches[1];
            $user_contexts[$user_hash] = ($user_contexts[$user_hash] ?? 0) + 1;
          } elseif (!preg_match('/user_|client_|global_/', $suffix)) {
            // Clave sin contexto aparente - posible colisión
            $potential_collisions[] = $key;
          }
        }
      }

      return [
        'total_keys_analyzed' => min(100, $total_keys),
        'total_keys_in_cache' => $total_keys,
        'key_patterns' => $key_patterns,
        'user_contexts_detected' => count($user_contexts),
        'user_context_distribution' => $user_contexts,
        'potential_collisions' => array_slice($potential_collisions, 0, 10), // Mostrar solo 10
        'collision_risk_level' => $this->_evaluar_riesgo_colision(count($potential_collisions), $total_keys),
        'isolation_effectiveness' => $this->_evaluar_efectividad_aislamiento($user_contexts, $potential_collisions)
      ];
    } catch (Exception $e) {
      return [
        'error' => 'Error detectando colisiones de caché',
        'details' => $e->getMessage()
      ];
    }
  }

  /**
   * Analiza los TTL (Time To Live) de los elementos en caché
   *
   * @return array
   */
  private function _analizar_ttl_cache(): array {
    try {
      $predis_client = $this->predis_cache_service->get_predis_client();
      if (!$predis_client) {
        return ['error' => 'Cliente Predis no disponible'];
      }

      $sample_keys = $predis_client->keys('*');
      $ttl_analysis = [
        'permanent' => 0,    // TTL = -1
        'expired' => 0,      // TTL = -2
        'short_term' => 0,   // TTL < 3600 (1 hora)
        'medium_term' => 0,  // TTL 3600-86400 (1-24 horas)
        'long_term' => 0     // TTL > 86400 (más de 24 horas)
      ];

      $ttl_distribution = [];
      $sample_size = min(50, count($sample_keys));

      foreach (array_slice($sample_keys, 0, $sample_size) as $key) {
        $ttl = $predis_client->ttl($key);

        if ($ttl === -1) {
          $ttl_analysis['permanent']++;
        } elseif ($ttl === -2) {
          $ttl_analysis['expired']++;
        } elseif ($ttl < 3600) {
          $ttl_analysis['short_term']++;
        } elseif ($ttl <= 86400) {
          $ttl_analysis['medium_term']++;
        } else {
          $ttl_analysis['long_term']++;
        }

        $ttl_distribution[] = [
          'key' => substr($key, 0, 50) . (strlen($key) > 50 ? '...' : ''),
          'ttl_seconds' => $ttl,
          'ttl_formatted' => $this->_format_ttl($ttl)
        ];
      }

      return [
        'sample_size' => $sample_size,
        'total_keys' => count($sample_keys),
        'ttl_categories' => $ttl_analysis,
        'ttl_distribution' => array_slice($ttl_distribution, 0, 20), // Mostrar solo 20
        'ttl_health_score' => $this->_calcular_score_ttl($ttl_analysis),
        'recommendations' => $this->_generar_recomendaciones_ttl($ttl_analysis)
      ];
    } catch (Exception $e) {
      return [
        'error' => 'Error analizando TTL de caché',
        'details' => $e->getMessage()
      ];
    }
  }

  /**
   * Genera recomendaciones basadas en las estadísticas
   *
   * @param array $cache_stats
   * @param array $performance_stats
   * @param array $memory_stats
   * @return array
   */
  private function _generar_recomendaciones(array $cache_stats, array $performance_stats, array $memory_stats): array {
    $recommendations = [];

    // Recomendaciones de rendimiento
    if (isset($performance_stats['hit_rate_percentage']) && $performance_stats['hit_rate_percentage'] < 80) {
      $recommendations[] = [
        'type' => 'performance',
        'priority' => 'high',
        'message' => 'Tasa de aciertos de caché baja (' . $performance_stats['hit_rate_percentage'] . '%). Considere revisar las estrategias de caché.',
        'action' => 'Analizar patrones de acceso y ajustar TTL de elementos frecuentemente accedidos'
      ];
    }

    // Recomendaciones de memoria
    if (isset($memory_stats['memory_usage_percentage']) && $memory_stats['memory_usage_percentage'] > 85) {
      $recommendations[] = [
        'type' => 'memory',
        'priority' => 'high',
        'message' => 'Uso de memoria alto (' . $memory_stats['memory_usage_percentage'] . '%). Riesgo de evicción de datos.',
        'action' => 'Considerar aumentar memoria disponible o implementar políticas de limpieza más agresivas'
      ];
    }

    // Recomendaciones de conexión
    if (!($cache_stats['connection_status'] ?? false)) {
      $recommendations[] = [
        'type' => 'connection',
        'priority' => 'critical',
        'message' => 'Conexión a Redis no disponible. Sistema funcionando en modo fallback.',
        'action' => 'Verificar configuración y estado del servidor Redis'
      ];
    }

    return $recommendations;
  }

  // Métodos auxiliares para cálculos y formateo
  private function _format_uptime($seconds): string {
    $days = floor($seconds / 86400);
    $hours = floor(($seconds % 86400) / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    return "{$days}d {$hours}h {$minutes}m";
  }

  private function _format_ttl($ttl): string {
    if ($ttl === -1) return 'Permanente';
    if ($ttl === -2) return 'Expirado';
    if ($ttl < 60) return $ttl . 's';
    if ($ttl < 3600) return round($ttl / 60) . 'm';
    if ($ttl < 86400) return round($ttl / 3600) . 'h';
    return round($ttl / 86400) . 'd';
  }

  private function _calcular_rating_rendimiento($hit_rate, $commands_per_second): string {
    if ($hit_rate >= 95 && $commands_per_second >= 1000) return 'Excelente';
    if ($hit_rate >= 85 && $commands_per_second >= 500) return 'Bueno';
    if ($hit_rate >= 70 && $commands_per_second >= 100) return 'Regular';
    return 'Necesita mejoras';
  }

  private function _calcular_eficiencia_memoria($usage_percentage): string {
    if ($usage_percentage < 50) return 'Óptima';
    if ($usage_percentage < 75) return 'Buena';
    if ($usage_percentage < 90) return 'Moderada';
    return 'Crítica';
  }

  private function _evaluar_riesgo_colision($collisions, $total_keys): string {
    $percentage = $total_keys > 0 ? ($collisions / $total_keys) * 100 : 0;
    if ($percentage < 1) return 'Bajo';
    if ($percentage < 5) return 'Moderado';
    return 'Alto';
  }

  private function _evaluar_efectividad_aislamiento($user_contexts, $potential_collisions): string {
    $total_contexts = count($user_contexts);
    $collisions = count($potential_collisions);

    if ($collisions === 0 && $total_contexts > 0) return 'Excelente';
    if ($collisions < 5) return 'Buena';
    return 'Necesita revisión';
  }

  private function _calcular_score_ttl($ttl_analysis): int {
    $total = array_sum($ttl_analysis);
    if ($total === 0) return 0;

    $score = 0;
    $score += ($ttl_analysis['short_term'] / $total) * 30;    // 30% peso para corto plazo
    $score += ($ttl_analysis['medium_term'] / $total) * 50;   // 50% peso para medio plazo
    $score += ($ttl_analysis['long_term'] / $total) * 20;     // 20% peso para largo plazo

    return round($score);
  }

  private function _generar_recomendaciones_ttl($ttl_analysis): array {
    $recommendations = [];
    $total = array_sum($ttl_analysis);

    if ($total > 0) {
      $permanent_percentage = ($ttl_analysis['permanent'] / $total) * 100;
      if ($permanent_percentage > 30) {
        $recommendations[] = 'Considere revisar elementos permanentes en caché';
      }

      $expired_percentage = ($ttl_analysis['expired'] / $total) * 100;
      if ($expired_percentage > 10) {
        $recommendations[] = 'Alto número de elementos expirados detectados';
      }
    }

    return $recommendations;
  }

  private function _generar_recomendaciones_memoria($usage_percentage, $used_bytes): array {
    $recommendations = [];

    if ($usage_percentage > 90) {
      $recommendations[] = 'Memoria crítica - Implementar limpieza inmediata';
    } elseif ($usage_percentage > 75) {
      $recommendations[] = 'Monitorear uso de memoria de cerca';
    }

    if ($used_bytes > 1073741824) { // 1GB
      $recommendations[] = 'Considerar optimización de datos almacenados';
    }

    return $recommendations;
  }

}
